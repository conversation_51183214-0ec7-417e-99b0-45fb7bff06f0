{"name": "Unity.RenderPipelines.Universal.Runtime", "rootNamespace": "", "references": ["GUID:df380645f10b7bc4b97d4f5eb6303d95", "GUID:69257879134bba646869b21467b3338d", "GUID:ab67fb10353d84448ac887a7367cbda8", "GUID:7dbf32976982c98448af054f2512cb79", "GUID:d8b63aba1907145bea998dd612889d6b", "GUID:2665a8d13d1b3f18800f46e256720795", "GUID:4fd6538c1c56b409fb53fdf0183170ec", "GUID:86bc95e6fdb13ff43aa04316542905ae", "GUID:d04eb9c554ad44ceab303cecf0c0cf82"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.visualeffectgraph", "expression": "0.0.1", "define": "VISUAL_EFFECT_GRAPH_0_0_1_OR_NEWER"}, {"name": "com.unity.adaptiveperformance", "expression": "2.0.0-preview.7", "define": "ADAPTIVE_PERFORMANCE_2_0_0_OR_NEWER"}, {"name": "com.unity.adaptiveperformance", "expression": "2.1.0", "define": "ADAPTIVE_PERFORMANCE_2_1_0_OR_NEWER"}, {"name": "com.unity.adaptiveperformance", "expression": "4.0.0-pre.1", "define": "ADAPTIVE_PERFORMANCE_4_0_0_OR_NEWER"}, {"name": "com.unity.burst", "expression": "1.0.0", "define": "ENABLE_BURST_1_0_0_OR_NEWER"}, {"name": "com.unity.modules.vr", "expression": "1.0.0", "define": "ENABLE_VR_MODULE"}, {"name": "com.unity.modules.xr", "expression": "1.0.0", "define": "ENABLE_XR_MODULE"}, {"name": "com.unity.modules.animation", "expression": "1.0.0", "define": "USING_ANIMATION_MODULE"}, {"name": "com.unity.modules.physics2d", "expression": "1.0.0", "define": "USING_PHYSICS2D_MODULE"}, {"name": "com.unity.inputsystem", "expression": "0.0.0", "define": "ENABLE_INPUT_SYSTEM_PACKAGE"}], "noEngineReferences": false}