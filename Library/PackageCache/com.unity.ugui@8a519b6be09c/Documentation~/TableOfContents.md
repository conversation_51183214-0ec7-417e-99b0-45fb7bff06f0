* [Unity UI](index.md)
  * [Unity UI: Unity User Interface](index.md)
  * [Canvas](UICanvas.md)
  * [Basic Layout](UIBasicLayout.md)
  * [Visual Components](UIVisualComponents.md)
  * [Interaction Components](UIInteractionComponents.md)
  * [Animation Integration](UIAnimationIntegration.md)
  * [Auto Layout](UIAutoLayout.md)
  * [Rich Text](StyledText.md)
  * [Events](EventSystem.md)
    * [MessagingSystem](MessagingSystem.md)
    * [InputModules](InputModules.md)
    * [SupportedEvents](SupportedEvents.md)
    * [Raycasters](Raycasters.md)
  * [Reference](UIReference.md)
    * [Rect Transform](class-RectTransform.md)
    * [Canvas Components](comp-CanvasComponents.md)
      * [Canvas](class-Canvas.md)
      * [Canvas Scaler](script-CanvasScaler.md)
      * [Canvas Group](class-CanvasGroup.md)
      * [Canvas Renderer](class-CanvasRenderer.md)
    * [Visual Components](comp-UIVisual.md)
      * [Text](script-Text.md)
      * [Image](script-Image.md)
      * [Raw Image](script-RawImage.md)
      * [Mask](script-Mask.md)
      * [RectMask2D](script-RectMask2D.md)
      * [UI Effect Components](comp-UIEffects.md)
        * [Shadow](script-Shadow.md)
        * [Outline](script-Outline.md)
        * [Position as UV1](script-PositionAsUV1.md)
    * [Interaction Components](comp-UIInteraction.md)
      * [Selectable Base Class](script-Selectable.md)
        * [Transition Options](script-SelectableTransition.md)
        * [Navigation Options](script-SelectableNavigation.md)
      * [Button](script-Button.md)
      * [Toggle](script-Toggle.md)
      * [Toggle Group](script-ToggleGroup.md)
      * [Slider](script-Slider.md)
      * [Scrollbar](script-Scrollbar.md)
      * [Dropdown](script-Dropdown.md)
      * [Input Field](script-InputField.md)
      * [Scroll Rect](script-ScrollRect.md)
    * [Auto Layout](comp-UIAutoLayout.md)
      * [Layout Element](script-LayoutElement.md)
      * [Content Size Fitter](script-ContentSizeFitter.md)
      * [Aspect Ratio Fitter](script-AspectRatioFitter.md)
      * [Horizontal Layout Group](script-HorizontalLayoutGroup.md)
      * [Vertical Layout Group](script-VerticalLayoutGroup.md)
      * [Grid Layout Group](script-GridLayoutGroup.md)
    * [Events](EventSystemReference.md)
      * [Event System Manager](script-EventSystem.md)
      * [Graphic Raycaster](script-GraphicRaycaster.md)
      * [Panel Event Handler](script-PanelEventHandler)
      * [Panel Raycaster](script-PanelRaycaster)
      * [Physics Raycaster](script-PhysicsRaycaster.md)
      * [Physics 2D Raycaster](script-Physics2DRaycaster.md)
      * [Standalone Input Module](script-StandaloneInputModule.md)
      * [Touch Input Module](script-TouchInputModule.md)
      * [Event Trigger](script-EventTrigger.md)
  * [UI How Tos](UIHowTos.md)
    * [Designing UI for Multiple Resolutions](HOWTO-UIMultiResolution.md)
    * [Making UI elements fit the size of their content](HOWTO-UIFitContentSize.md)
    * [Creating a World Space UI](HOWTO-UIWorldSpace.md)
    * [Creating UI elements from scripting](HOWTO-UICreateFromScripting.md)
    * [Creating Screen Transitions](HOWTO-UIScreenTransition.md)
    * [Creating Custom UI Effects With Shader Graph](HOWTO-ShaderGraph.md)
  * [UI and UI Details Profiler](ProfilerUI.md)  
* [TextMesh Pro](TextMeshPro/index)
  * [Creating text](TextMeshPro/TMPObjects)
    * [UI Text GameObjects](TextMeshPro/TMPObjectUIText)
    * [3D Text GameObjects](TextMeshPro/TMPObject3DText)
  * [Font Assets](TextMeshPro/FontAssets)
    * [Font Asset Properties](TextMeshPro/FontAssetsProperties)
    * [Font Asset Creator](TextMeshPro/FontAssetsCreator)
    * [Line Metrics](TextMeshPro/FontAssetsLineMetrics)
    * [Signed Distance Fields](TextMeshPro/FontAssetsSDF)
    * [Dynamic Fonts](TextMeshPro/FontAssetsDynamicFonts)
    * [The Fallback Chain](TextMeshPro/FontAssetsFallback)
    * [Color emojis](TextMeshPro/ColorEmojis)
  * [Rich Text Tags](TextMeshPro/RichText)
    * [Supported Tags](TextMeshPro/RichTextSupportedTags)
    * [<align>](TextMeshPro/RichTextAlignment)
    * [<allcaps>](TextMeshPro/RichTextLetterCase)
    * [<alpha>](TextMeshPro/RichTextOpacity)
    * [<b>](TextMeshPro/RichTextBoldItalic)
    * [<color>](TextMeshPro/RichTextColor)
    * [<cspace>](TextMeshPro/RichTextCharacterSpacing)
    * [<font>](TextMeshPro/RichTextFont)
    * [<font-weight>](TextMeshPro/RichTextFontWeight)
    * [<gradient>](TextMeshPro/RichTextGradient)
    * [<i>](TextMeshPro/RichTextBoldItalic)
    * [<indent>](TextMeshPro/RichTextIndentation)
    * [<line-height>](TextMeshPro/RichTextLineHeight)
    * [<line-indent>](TextMeshPro/RichTextLineIndentation)
    * [<link>](TextMeshPro/RichTextLink)
    * [<lowercase>](TextMeshPro/RichTextLetterCase)
    * [<margin>](TextMeshPro/RichTextMargins)
    * [<mark>](TextMeshPro/RichTextMark)
    * [<mspace>](TextMeshPro/RichTextMonospace)
    * [<nobr>](TextMeshPro/RichTextNoBreak)
    * [<noparse>](TextMeshPro/RichTextNoParse)
    * [<page>](TextMeshPro/RichTextPageBreak)
    * [<pos>](TextMeshPro/RichTextPos)
    * [<rotate>](TextMeshPro/RichTextRotate)
    * [<s>](TextMeshPro/RichTextStrikethroughUnderline)
    * [<size>](TextMeshPro/RichTextSize)
    * [<smallcaps>](TextMeshPro/RichTextLetterCase)
    * [<space>](TextMeshPro/RichTextSpace)
    * [<sprite>](TextMeshPro/RichTextSprite)
    * [<style>](TextMeshPro/RichTextStyle)
    * [<sub>](TextMeshPro/RichTextSubSuper)
    * [<sup>](TextMeshPro/RichTextSubSuper)
    * [<u>](TextMeshPro/RichTextStrikethroughUnderline)
    * [<uppercase>](TextMeshPro/RichTextLetterCase)
    * [<voffset>](TextMeshPro/RichTextVOffset)
    * [<width>](TextMeshPro/RichTextWidth)
  * [Style Sheets](TextMeshPro/StyleSheets)
  * [Sprites](TextMeshPro/Sprites)
  * [Color Gradients](TextMeshPro/ColorGradients)
    * [Color Gradient Types](TextMeshPro/ColorGradientsTypes)
    * [Color Gradient Presets](TextMeshPro/ColorGradientsPresets)
  * [Shaders](TextMeshPro/Shaders)
    * [SDF Shaders]()
      * [Distance Field](TextMeshPro/ShadersDistanceField)
      * [Distance Field Overlay](TextMeshPro/ShadersDistanceField)
      * [Distance Field (Surface)](TextMeshPro/ShadersDistanceFieldSurface)
      * [Distance Field - Mobile](TextMeshPro/ShadersDistanceFieldMobile)
      * [Distance Field Masking - Mobile](TextMeshPro/ShadersDistanceFieldMaskingMobile)
      * [Distance Field Overlay - Mobile](TextMeshPro/ShadersDistanceFieldMobile)
      * [Distance Field (Surface) - Mobile](TextMeshPro/ShadersDistanceFieldSurfaceMobile)
    * [Bitmap Shaders]()
      * [Bitmap](TextMeshPro/ShadersBitmap)
      * [Bitmap Custom Atlas](TextMeshPro/ShadersBitmapCustomAtlas)
      * [Bitmap - MobileBitmapMobile](TextMeshPro/Shaders)
  * [Settings](TextMeshPro/Settings)
