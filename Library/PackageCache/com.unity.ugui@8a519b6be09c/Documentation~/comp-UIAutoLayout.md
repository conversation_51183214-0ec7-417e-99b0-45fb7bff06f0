# Auto Layout

The [auto layout](UIAutoLayout.md) system provides ways to place elements in nested layout groups such as horizontal groups, vertical groups, or grids. It also allows elements to automatically be sized according to the contained content.

| Topic | Description |
| --- | --- |
| [Layout Element](script-LayoutElement.md) | Controls the minimum, preferred, and flexible sizes of a layout element. |
| [Content Size Fitter](script-ContentSizeFitter.md) | Controls the size of its own layout element. |
| [Aspect Ratio Fitter](script-AspectRatioFitter.md) | Sizes the RectTransform to maintain a specific aspect ratio. |
| [Horizontal Layout Group](script-HorizontalLayoutGroup.md) | Arranges child elements in a horizontal row. |
| [Vertical Layout Group](script-VerticalLayoutGroup.md) | Arranges child elements in a vertical column. |
| [Grid Layout Group](script-GridLayoutGroup.md) | Arranges child elements in a grid. |
