| Tag: | Description: | Notes:|
|-------------|-------------|-|
|[`<align>`](RichTextAlignment.md)|Changes the text's horizontal alignment.||
|[`<allcaps>`](RichTextAlignment.md)|Converts text to uppercase before rendering.|Functionally identical to `<uppercase>`.|
|[`<alpha>`](RichTextOpacity.md)|Changes text opacity.||
|[`<b>`](RichTextBoldItalic.md)|Renders text in boldface.||
|[`<br>`](RichTextLineBreak.md)   | Forces a line break in text.   |  |
|[`<color>`](RichTextColor.md)|Changes text color or color and opacity.||
|[`<cspace>`](RichTextCharacterSpacing.md)|Changes spacing between characters.||
|[`<font>`](RichTextFont.md)|Changes text font and, optionally, material.||
|[`<font-weight>`](RichTextFontWeight.md)   | Changes the text's font weight to any of the weights defined in the [font Asset](FontAssetsProperties.md).  | |  
|[`<gradient>`](RichTextGradient.md)   | Applies a [gradient preset](ColorGradientsPresets.md) to text.  | |  
|[`<i>`](RichTextBoldItalic.md)|Renders text in italics.||
|[`<indent>`](RichTextIndentation.md)|Indents all text between the tag and the next hard line break.||
|[`<line-height>`](RichTextLineHeight.md)|Modifies the line height relative to the default line height specified in the font Asset.||
|[`<line-indent>`](RichTextLineIndentation.md)|Indents the first line after every hard line break.|New lines created by word-wrapping are not indented.|
|[`<link>`](RichTextLink.md)|Specifies a link ID for a text segment.||
|[`<lowercase>`](RichTextLetterCase.md)|Converts text to lowercase before rendering.||
|[`<margin>`](RichTextMargins.md)|Gives the text horizontal margins.|You can set margins for both sides together or each side individually|
|[`<mark>`](RichTextMark.md)|Highlights the text with a colored overlay.|The overlay must be translucent (alpha less than 1) for the text to show through.|
|[`<mspace>`](RichTextMonospace.md)|Renders the text as monospace.||
|[`<nobr>`](RichTextNoBreak.md)|Keeps a segment of text together.||
|[`<noparse>`](RichTextNoParse.md)|Prevents parsing of text that TextMesh Pro would normally interpret  as rich text tags.||
|[`<page>`](RichTextPageBreak.md)|Adds a page break to the text.|The text's [**Overflow** mode](TMPObjectUIText.md#wrapping) must be set to **Page** for page breaks to work.|
|[`<pos>`](RichTextPos.md)|Sets the horizontal caret position on the current line.||
|[`<rotate>`](RichTextRotate.md)  | Rotates each character about its center.  |  |
|[`<s>`](RichTextStrikethroughUnderline.md)|Renders a line across the text.||
|[`<size>`](RichTextSize.md)|Adjusts the font size for a specified portion of the text.||
|[`<smallcaps>`](RichTextLetterCase.md)|Converts text to uppercase before rendering.||
|[`<space>`](RichTextSpace.md)|Adds a horizontal offset between itself and the rest of the text.||
|[`<sprite>`](RichTextSprite.md)|Adds a [sprite](Srites.md) to the text.|By default, TextMesh Pro looks in the default sprite assets, but you can use tag attributes to retrieve sprites from other assets.|
|[`<strikethrough>`](RichTextStrikethroughUnderline.md)  | Draws a line slightly above the baseline so it crosses out the text.  ||
|[`<style>`](RichTextStyle.md)|Applies a custom style to the text.||
|[`<sub>`](RichTextSubSuper.md)|Converts the text to subscript.||
|[`<sup>`](RichTextSubSuper.md)|Converts the test to superscript.||
|[`<u>`](RichTextStrikethroughUnderline.md)|Draws a line slightly below the baseline to underline the text.||
|[`<uppercase>`](RichTextLetterCase.md)|Converts text to uppercase before rendering.|Functionally identical to `<allcaps>`.|
|[`<voffset>`](RichTextVOffset.md)|Gives the baseline a vertical offset.||
|[`<width>`](RichTextWidth.md)|Changes the horizontal size of text area.||