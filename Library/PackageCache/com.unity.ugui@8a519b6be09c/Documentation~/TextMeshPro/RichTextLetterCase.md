# Lowercase, Uppercase, and Smallcaps

The `<lowercase>`, `<uppercase>`, `<allcaps>` and `<smallcaps>` tags alter the capitalization of your text before rendering. The text in the **Text** field remains as you entered it.

* The `<lowercase>` and `<uppercase>` tags work as you would expect, converting to all capitals or no capitals before rendering.

* The `<allcaps>` tag is functionally identical to `<uppercase>`.

* The `<smallcaps>` tag works like `<uppercase>`, but also reduces the size of all characters that you entered in lowercase.

**Example:**

```
<lowercase><PERSON> and <PERSON> watched TV.</lowercase>
<uppercase><PERSON> and <PERSON> watched TV.</uppercase>
<allcaps><PERSON> and <PERSON> watched TV.</allcaps>
<smallcaps><PERSON> and <PERSON> watched TV.</smallcaps>
```
![Example image](../images/TMP_RichTextLetterCase.png)<br/>
_Modifying capitalization._
