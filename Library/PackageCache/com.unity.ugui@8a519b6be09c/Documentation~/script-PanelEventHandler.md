# Panel Event Handler

Use this class to handle input and send events to UI Toolkit runtime panels.

For additional information about using input and event systems with UI Toolkit,
refer to [FAQ for event and input system](https://docs.unity3d.com/Manual/UIE-faq-event-and-input-system.html).

## Properties

|**Property:** |**Function:** |
|:---|:---|
|`panel`| The panel that this component relates to. If the panel is null, this component has no effect. It will automatically be set to `null` automatically if the panel is disposed of from an external source. |